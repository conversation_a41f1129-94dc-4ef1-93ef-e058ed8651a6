// Copyright Epic Games, Inc. All Rights Reserved.

#include "ApiRequests/ExecuteTurnRequest.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "AgentBridge.h"

UExecuteTurnRequest::UExecuteTurnRequest()
{
    // Constructor
}

void UExecuteTurnRequest::SetWorldId(const FString& InWorldId)
{
    WorldId = InWorldId;
}

void UExecuteTurnRequest::SetTurnData(const FTurnExecutionRequest& InTurnData)
{
    TurnData = InTurnData;
}

FString UExecuteTurnRequest::GetVerb() const
{
    return TEXT("POST");
}

FString UExecuteTurnRequest::GetEndpoint() const
{
    return FString::Printf(TEXT("/api/world-engine/%s/execute-turn"), *WorldId);
}

FString UExecuteTurnRequest::GetRequestBody() const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

    // Add agent IDs array
    TArray<TSharedPtr<FJsonValue>> AgentIdsArray;
    for (const FString& AgentId : TurnData.AgentIds)
    {
        AgentIdsArray.Add(MakeShareable(new FJsonValueString(AgentId)));
    }
    JsonObject->SetArrayField(TEXT("agent_ids"), AgentIdsArray);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

void UExecuteTurnRequest::ProcessResponse(const FString& Response, bool bWasSuccessful, int32 StatusCode)
{
    Super::ProcessResponse(Response, bWasSuccessful, StatusCode);

    if (bWasSuccessful && StatusCode == 200)
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Parse basic turn response data
            TurnResponse.Turn = JsonObject->GetIntegerField(TEXT("turn"));
            TurnResponse.WorldId = JsonObject->GetStringField(TEXT("world_id"));
            TurnResponse.Timestamp = JsonObject->GetStringField(TEXT("timestamp"));

            // Parse agent results
            const TSharedPtr<FJsonObject>* AgentResultsObject;
            if (JsonObject->TryGetObjectField(TEXT("agent_results"), AgentResultsObject))
            {
                for (const auto& AgentResultPair : (*AgentResultsObject)->Values)
                {
                    FString AgentId = AgentResultPair.Key;
                    const TSharedPtr<FJsonObject>* ResultObject;

                    if (AgentResultPair.Value->TryGetObject(ResultObject))
                    {
                        FAgentTurnResult TurnResult;

                        // Parse turn result fields
                        (*ResultObject)->TryGetStringField(TEXT("observation"), TurnResult.Observation);
                        (*ResultObject)->TryGetStringField(TEXT("action"), TurnResult.Action);
                        (*ResultObject)->TryGetBoolField(TEXT("success"), TurnResult.bSuccess);
                        (*ResultObject)->TryGetNumberField(TEXT("feasibility_score"), TurnResult.FeasibilityScore);
                        (*ResultObject)->TryGetStringField(TEXT("failure_reason"), TurnResult.FailureReason);
                        (*ResultObject)->TryGetStringField(TEXT("updated_status"), TurnResult.UpdatedStatus);
                        (*ResultObject)->TryGetStringField(TEXT("error"), TurnResult.Error);

                        // Add to parallel arrays
                        TurnResponse.AgentIds.Add(AgentId);
                        TurnResponse.AgentResults.Add(TurnResult);
                    }
                }
            }

            UE_LOG(LogAgentBridge, Log, TEXT("Successfully executed turn %d for world: %s"), TurnResponse.Turn, *TurnResponse.WorldId);
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse turn execution response"));
        }
    }
}

FTurnExecutionResponse UExecuteTurnRequest::GetTurnResponse() const
{
    return TurnResponse;
}
